<!--
	Dashboard Page - Protected Route

	This is a simple dashboard page to test the authentication flow.
	It displays user information and provides a logout button.
	This page should only be accessible to authenticated users.
-->

<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "$lib/components/ui/card";
	import { Badge } from "$lib/components/ui/badge";
	import { authStore } from "$lib/stores/auth.svelte";
	import { toast } from "svelte-sonner";

	// Get reactive auth state
	const { state } = authStore;

	/**
	 * Handle user logout
	 */
	function handleLogout() {
		authStore.logout();
		toast.success("Logged out successfully");
	}

	/**
	 * Format date for display
	 */
	function formatDate(dateString: string | null): string {
		if (!dateString) return 'Never';
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}
</script>

<svelte:head>
	<title>Dashboard - Evoprof</title>
	<meta name="description" content="User dashboard for Evoprof platform" />
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8">
	<div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="mb-8">
			<div class="flex items-center justify-between">
				<div>
					<h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
					<p class="mt-2 text-gray-600">Welcome back to Evoprof!</p>
				</div>
				<Button variant="outline" onclick={handleLogout}>
					Logout
				</Button>
			</div>
		</div>

		{#if state.user}
			<!-- User Information Card -->
			<div class="mb-8">
				<Card>
					<CardHeader>
						<CardTitle>User Profile</CardTitle>
						<CardDescription>Your account information and details</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label class="text-sm font-medium text-gray-500">Username</label>
								<p class="text-lg font-semibold">{state.user.username}</p>
							</div>
							<div>
								<label class="text-sm font-medium text-gray-500">Email</label>
								<p class="text-lg">{state.user.email}</p>
							</div>
							<div>
								<label class="text-sm font-medium text-gray-500">User ID</label>
								<p class="text-sm font-mono text-gray-600">{state.user.id}</p>
							</div>
							<div>
								<label class="text-sm font-medium text-gray-500">Account Status</label>
								<div class="flex items-center gap-2">
									<Badge variant={state.user.is_active ? "default" : "destructive"}>
										{state.user.is_active ? "Active" : "Inactive"}
									</Badge>
									{#if state.user.is_staff}
										<Badge variant="secondary">Staff</Badge>
									{/if}
								</div>
							</div>
							<div>
								<label class="text-sm font-medium text-gray-500">Last Login</label>
								<p class="text-sm">{formatDate(state.user.last_login)}</p>
							</div>
							<div>
								<label class="text-sm font-medium text-gray-500">Account Created</label>
								<p class="text-sm">{formatDate(state.user.created_at)}</p>
							</div>
						</div>

						{#if state.user.user_role}
							<div class="border-t pt-4">
								<label class="text-sm font-medium text-gray-500">Role & Permissions</label>
								<div class="mt-2">
									<Badge variant="outline" class="mb-2">
										{state.user.user_role.role_name}
									</Badge>
									{#if state.user.user_role.permissions.length > 0}
										<div class="mt-2">
											<p class="text-sm text-gray-600 mb-2">Permissions:</p>
											<div class="flex flex-wrap gap-1">
												{#each state.user.user_role.permissions as permission}
													<Badge variant="secondary" class="text-xs">
														{permission.permission_name}
													</Badge>
												{/each}
											</div>
										</div>
									{:else}
										<p class="text-sm text-gray-500 mt-2">No specific permissions assigned</p>
									{/if}
								</div>
							</div>
						{:else}
							<div class="border-t pt-4">
								<p class="text-sm text-gray-500">No role assigned</p>
							</div>
						{/if}
					</CardContent>
				</Card>
			</div>

			<!-- Authentication Status Card -->
			<div class="mb-8">
				<Card>
					<CardHeader>
						<CardTitle>Authentication Status</CardTitle>
						<CardDescription>Current session and token information</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label class="text-sm font-medium text-gray-500">Authentication Status</label>
								<Badge variant={state.isAuthenticated ? "default" : "destructive"}>
									{state.isAuthenticated ? "Authenticated" : "Not Authenticated"}
								</Badge>
							</div>
							<div>
								<label class="text-sm font-medium text-gray-500">Session Active</label>
								<Badge variant={state.accessToken ? "default" : "destructive"}>
									{state.accessToken ? "Active" : "Inactive"}
								</Badge>
							</div>
						</div>

						{#if state.accessToken}
							<div>
								<label class="text-sm font-medium text-gray-500">Access Token (First 20 chars)</label>
								<p class="text-sm font-mono text-gray-600">
									{state.accessToken.substring(0, 20)}...
								</p>
							</div>
						{/if}
					</CardContent>
				</Card>
			</div>

			<!-- Quick Actions -->
			<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
				<Card>
					<CardContent class="p-6 text-center">
						<h3 class="font-semibold mb-2">Profile Settings</h3>
						<p class="text-sm text-gray-600 mb-4">Update your profile information</p>
						<Button variant="outline" class="w-full" disabled>
							Coming Soon
						</Button>
					</CardContent>
				</Card>

				<Card>
					<CardContent class="p-6 text-center">
						<h3 class="font-semibold mb-2">Security</h3>
						<p class="text-sm text-gray-600 mb-4">Manage your account security</p>
						<Button variant="outline" class="w-full" disabled>
							Coming Soon
						</Button>
					</CardContent>
				</Card>

				<Card>
					<CardContent class="p-6 text-center">
						<h3 class="font-semibold mb-2">Support</h3>
						<p class="text-sm text-gray-600 mb-4">Get help and support</p>
						<Button variant="outline" class="w-full" href="/contact">
							Contact Support
						</Button>
					</CardContent>
				</Card>
			</div>
		{:else}
			<!-- Not authenticated state -->
			<Card>
				<CardContent class="p-8 text-center">
					<h2 class="text-xl font-semibold mb-4">Authentication Required</h2>
					<p class="text-gray-600 mb-6">You need to be logged in to access this page.</p>
					<Button href="/login">
						Go to Login
					</Button>
				</CardContent>
			</Card>
		{/if}
	</div>
</div>
