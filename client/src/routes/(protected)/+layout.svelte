<!--
	Protected Routes Layout

	This layout wraps all protected routes and ensures that only authenticated
	users can access them. It provides automatic redirects to the login page
	for unauthenticated users and handles loading states.
-->

<script lang="ts">
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { authStore } from '$lib/stores/auth.svelte';
	import { onMount } from 'svelte';

	// Get reactive auth state
	const { state } = authStore;

	// Track if we've completed the initial auth check
	let authCheckComplete = $state(false);

	/**
	 * Check authentication status and redirect if necessary
	 */
	function checkAuth() {
		if (!browser) return;

		// If not authenticated, redirect to login with return URL
		if (!state.isAuthenticated && !state.isLoading) {
			const returnUrl = encodeURIComponent($page.url.pathname + $page.url.search);
			goto(`/login?redirectTo=${returnUrl}`);
			return;
		}

		authCheckComplete = true;
	}

	/**
	 * Initialize authentication check when component mounts
	 */
	onMount(() => {
		// Give the auth store a moment to initialize from localStorage
		setTimeout(checkAuth, 100);
	});

	/**
	 * Watch for changes in authentication state
	 */
	$effect(() => {
		if (browser && authCheckComplete) {
			checkAuth();
		}
	});
</script>

{#if browser}
	{#if state.isLoading || !authCheckComplete}
		<!-- Loading state while checking authentication -->
		<div class="flex min-h-screen items-center justify-center">
			<div class="text-center">
				<div class="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
				<p class="text-gray-600">Checking authentication...</p>
			</div>
		</div>
	{:else if state.isAuthenticated}
		<!-- User is authenticated, show the protected content -->
		<slot />
	{:else}
		<!-- User is not authenticated, show fallback (shouldn't normally be seen due to redirect) -->
		<div class="flex min-h-screen items-center justify-center">
			<div class="text-center">
				<h1 class="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
				<p class="text-gray-600 mb-6">You need to be logged in to access this page.</p>
				<a
					href="/login"
					class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
				>
					Go to Login
				</a>
			</div>
		</div>
	{/if}
{:else}
	<!-- Server-side rendering fallback -->
	<div class="flex min-h-screen items-center justify-center">
		<div class="text-center">
			<div class="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
			<p class="text-gray-600">Loading...</p>
		</div>
	</div>
{/if}
