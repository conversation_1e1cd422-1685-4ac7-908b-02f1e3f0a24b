/**
 * Client-side API route for authentication
 * 
 * This API route provides an alternative to server actions for client-side
 * authentication. It handles login requests from the frontend and communicates
 * with the Django backend, providing a clean separation between client and server logic.
 */

import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import type { LoginCredentials, LoginResponse, AuthError } from '$types/auth';

/**
 * Handle POST requests for user login
 * 
 * This endpoint receives login credentials from the client, validates them
 * with the Django backend, and returns the authentication response.
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		// Parse request body
		const body = await request.json();
		const { email, password }: LoginCredentials = body;

		// Basic validation
		if (!email || !password) {
			return json(
				{
					success: false,
					error: {
						detail: 'Email and password are required'
					}
				},
				{ status: 400 }
			);
		}

		// Make request to Django backend
		const response = await fetch('http://localhost:8000/auth/login/', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				email,
				password
			})
		});

		// Parse backend response
		const data = await response.json();

		if (response.ok) {
			// Login successful
			const loginResponse: LoginResponse = data;

			// Optionally set secure cookies for token storage
			// This provides an alternative to localStorage for token persistence
			cookies.set('access_token', loginResponse.access, {
				path: '/',
				httpOnly: true,
				secure: process.env.NODE_ENV === 'production',
				sameSite: 'strict',
				maxAge: 60 * 15 // 15 minutes
			});

			cookies.set('refresh_token', loginResponse.refresh, {
				path: '/',
				httpOnly: true,
				secure: process.env.NODE_ENV === 'production',
				sameSite: 'strict',
				maxAge: 60 * 60 * 24 * 7 // 7 days
			});

			// Return success response
			return json({
				success: true,
				data: loginResponse
			});

		} else {
			// Login failed
			const authError: AuthError = data;
			
			return json(
				{
					success: false,
					error: authError,
					message: authError.detail || 'Login failed'
				},
				{ status: response.status }
			);
		}

	} catch (error) {
		console.error('Login API error:', error);

		// Handle network errors and other exceptions
		return json(
			{
				success: false,
				error: {
					detail: 'Internal server error'
				},
				message: 'An unexpected error occurred'
			},
			{ status: 500 }
		);
	}
};

/**
 * Handle OPTIONS requests for CORS preflight
 * This is useful if the frontend and backend are on different domains
 */
export const OPTIONS: RequestHandler = async () => {
	return new Response(null, {
		status: 200,
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'POST, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization',
		},
	});
};
