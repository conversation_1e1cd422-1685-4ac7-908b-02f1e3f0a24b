/**
 * Server-side form actions for the login page
 * 
 * This file handles server-side form processing for the login page using SvelteKit's
 * form actions. It integrates with Superforms for validation and provides proper
 * error handling and response formatting.
 */

import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { loginSchema } from '$lib/schemas/auth';
import type { Actions, PageServerLoad } from './$types';

/**
 * Page load function
 * 
 * Initializes the login form with Superforms and handles redirects
 * for already authenticated users.
 */
export const load: PageServerLoad = async ({ url, locals }) => {
	// Initialize the form with the login schema
	const form = await superValidate(zod(loginSchema));

	// Check if user is already authenticated (if you have session handling)
	// For now, we'll handle this client-side in the component
	
	// Get redirect URL from query parameters (for post-login redirect)
	const redirectTo = url.searchParams.get('redirectTo') || '/dashboard';

	return {
		form,
		redirectTo
	};
};

/**
 * Form actions for the login page
 */
export const actions: Actions = {
	/**
	 * Default login action
	 * 
	 * Handles form submission, validates input, and communicates with the backend API.
	 * Returns appropriate success/error responses for the client to handle.
	 */
	default: async ({ request, cookies, url }) => {
		// Parse and validate the form data
		const form = await superValidate(request, zod(loginSchema));

		// Return validation errors if form is invalid
		if (!form.valid) {
			return fail(400, {
				form,
				message: 'Please correct the errors below.'
			});
		}

		// Extract validated form data
		const { email, password } = form.data;

		try {
			// Make API request to Django backend
			const response = await fetch('http://localhost:8000/auth/login/', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					email,
					password
				})
			});

			// Parse response
			const data = await response.json();

			if (response.ok) {
				// Login successful - store tokens in secure cookies
				const { access, refresh, user } = data;

				// Set secure HTTP-only cookies for tokens
				// These will be automatically sent with subsequent requests
				cookies.set('access_token', access, {
					path: '/',
					httpOnly: true,
					secure: process.env.NODE_ENV === 'production',
					sameSite: 'strict',
					maxAge: 60 * 15 // 15 minutes (typical JWT access token lifetime)
				});

				cookies.set('refresh_token', refresh, {
					path: '/',
					httpOnly: true,
					secure: process.env.NODE_ENV === 'production',
					sameSite: 'strict',
					maxAge: 60 * 60 * 24 * 7 // 7 days (typical refresh token lifetime)
				});

				// Store user data in a separate cookie (not HTTP-only so client can access)
				cookies.set('user_data', JSON.stringify(user), {
					path: '/',
					secure: process.env.NODE_ENV === 'production',
					sameSite: 'strict',
					maxAge: 60 * 60 * 24 * 7 // 7 days
				});

				// Get redirect URL from form or query parameters
				const redirectTo = url.searchParams.get('redirectTo') || '/dashboard';

				// Redirect to the intended destination
				throw redirect(302, redirectTo);

			} else {
				// Login failed - handle different error types
				let errorMessage = 'Login failed. Please try again.';
				
				if (data.detail) {
					errorMessage = data.detail;
				} else if (data.non_field_errors && data.non_field_errors.length > 0) {
					errorMessage = data.non_field_errors[0];
				} else if (response.status === 401) {
					errorMessage = 'Invalid email or password. Please check your credentials and try again.';
				} else if (response.status >= 500) {
					errorMessage = 'Server error. Please try again later.';
				}

				// Return form with error message
				return fail(response.status, {
					form,
					message: errorMessage,
					error: data
				});
			}

		} catch (error) {
			console.error('Login error:', error);

			// Handle network errors and other exceptions
			let errorMessage = 'An unexpected error occurred. Please try again.';
			
			if (error instanceof Error) {
				// Don't expose internal error details to users in production
				if (process.env.NODE_ENV === 'development') {
					errorMessage = error.message;
				}
			}

			return fail(500, {
				form,
				message: errorMessage
			});
		}
	}
};
