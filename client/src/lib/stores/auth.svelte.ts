/**
 * Authentication Store using Svelte 5 Runes
 *
 * This store manages the global authentication state of the application.
 * It handles user login/logout, token management, session persistence,
 * and provides reactive authentication state to components.
 */

import { browser } from "$app/environment";
import { goto } from "$app/navigation";
import { authApi } from "$lib/api/client";
import { AUTH_CONFIG, APP_CONFIG, debugLog } from "$lib/config/env";
import type { User, LoginCredentials, AuthState } from "$types/auth";

/**
 * Initial authentication state
 */
const initialState: AuthState = {
	user: null,
	accessToken: null,
	refreshToken: null,
	isAuthenticated: false,
	isLoading: false,
	error: null
};

/**
 * Create reactive authentication state using Svelte 5 runes
 */
function createAuthStore() {
	// Reactive state using $state rune
	const state = $state<AuthState>({ ...initialState });

	/**
	 * Utility functions for local storage operations
	 * Only run in browser environment to avoid SSR issues
	 */
	const storage = {
		get: (key: string): string | null => {
			if (!browser) return null;
			try {
				return localStorage.getItem(key);
			} catch {
				return null;
			}
		},

		set: (key: string, value: string): void => {
			if (!browser) return;
			try {
				localStorage.setItem(key, value);
			} catch {
				// Silently fail if localStorage is not available
			}
		},

		remove: (key: string): void => {
			if (!browser) return;
			try {
				localStorage.removeItem(key);
			} catch {
				// Silently fail if localStorage is not available
			}
		},

		clear: (): void => {
			if (!browser) return;
			try {
				Object.values(STORAGE_KEYS).forEach((key) => {
					localStorage.removeItem(key);
				});
			} catch {
				// Silently fail if localStorage is not available
			}
		}
	};

	/**
	 * Initialize authentication state from localStorage
	 * This should be called when the app starts
	 */
	function initialize(): void {
		if (!browser) return;

		try {
			const accessToken = storage.get(AUTH_CONFIG.STORAGE_KEYS.ACCESS_TOKEN);
			const refreshToken = storage.get(AUTH_CONFIG.STORAGE_KEYS.REFRESH_TOKEN);
			const userJson = storage.get(AUTH_CONFIG.STORAGE_KEYS.USER);

			if (accessToken && refreshToken && userJson) {
				const user = JSON.parse(userJson) as User;

				// Update state
				state.accessToken = accessToken;
				state.refreshToken = refreshToken;
				state.user = user;
				state.isAuthenticated = true;

				// Set token in API client
				authApi.setAccessToken(accessToken);
				debugLog("Auth state initialized from localStorage");
			}
		} catch (error) {
			console.error("Failed to initialize auth state:", error);
			// Clear corrupted data
			clearAuthData();
		}
	}

	/**
	 * Save authentication data to localStorage
	 */
	function saveAuthData(user: User, accessToken: string, refreshToken: string): void {
		storage.set(AUTH_CONFIG.STORAGE_KEYS.ACCESS_TOKEN, accessToken);
		storage.set(AUTH_CONFIG.STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
		storage.set(AUTH_CONFIG.STORAGE_KEYS.USER, JSON.stringify(user));
		debugLog("Auth data saved to localStorage");
	}

	/**
	 * Clear authentication data from localStorage
	 */
	function clearAuthData(): void {
		Object.values(AUTH_CONFIG.STORAGE_KEYS).forEach((key) => {
			storage.remove(key);
		});
		authApi.setAccessToken(null);
		debugLog("Auth data cleared from localStorage");
	}

	/**
	 * Login user with email and password
	 *
	 * @param credentials - User login credentials
	 * @returns Promise that resolves to success status
	 */
	async function login(credentials: LoginCredentials): Promise<boolean> {
		// Set loading state
		state.isLoading = true;
		state.error = null;

		try {
			const response = await authApi.login(credentials);

			if (response.success && response.data) {
				const { user, access, refresh } = response.data;

				// Update state
				state.user = user;
				state.accessToken = access;
				state.refreshToken = refresh;
				state.isAuthenticated = true;
				state.error = null;

				// Persist to localStorage
				saveAuthData(user, access, refresh);

				return true;
			} else {
				// Handle login failure
				const errorMessage =
					response.error?.detail ||
					response.error?.non_field_errors?.[0] ||
					response.message ||
					"Login failed. Please check your credentials.";

				state.error = errorMessage;
				return false;
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
			state.error = errorMessage;
			return false;
		} finally {
			state.isLoading = false;
		}
	}

	/**
	 * Logout user and clear all authentication data
	 *
	 * @param redirectTo - Optional path to redirect to after logout
	 */
	function logout(redirectTo: string = APP_CONFIG.ROUTES.LOGIN): void {
		// Clear state
		state.user = null;
		state.accessToken = null;
		state.refreshToken = null;
		state.isAuthenticated = false;
		state.error = null;

		// Clear stored data
		clearAuthData();

		// Redirect to login page
		if (browser) {
			goto(redirectTo);
		}
	}

	/**
	 * Refresh the access token using the refresh token
	 *
	 * @returns Promise that resolves to success status
	 */
	async function refreshAccessToken(): Promise<boolean> {
		if (!state.refreshToken) {
			logout();
			return false;
		}

		try {
			const response = await authApi.refreshToken(state.refreshToken);

			if (response.success && response.data) {
				// Update access token
				state.accessToken = response.data.access;
				storage.set(AUTH_CONFIG.STORAGE_KEYS.ACCESS_TOKEN, response.data.access);
				return true;
			} else {
				// Refresh failed, logout user
				logout();
				return false;
			}
		} catch (error) {
			console.error("Token refresh failed:", error);
			logout();
			return false;
		}
	}

	/**
	 * Check if user has a specific permission
	 *
	 * @param permission - Permission name to check
	 * @returns True if user has the permission
	 */
	function hasPermission(permission: string): boolean {
		if (!state.user?.user_role?.permissions) return false;
		return state.user.user_role.permissions.some((p) => p.permission_name === permission);
	}

	/**
	 * Check if user has a specific role
	 *
	 * @param role - Role name to check
	 * @returns True if user has the role
	 */
	function hasRole(role: string): boolean {
		return state.user?.user_role?.role_name === role;
	}

	/**
	 * Clear any authentication errors
	 */
	function clearError(): void {
		state.error = null;
	}

	// Return the store interface
	return {
		// Reactive state (read-only)
		get state() {
			return state;
		},

		// Actions
		initialize,
		login,
		logout,
		refreshAccessToken,
		hasPermission,
		hasRole,
		clearError
	};
}

/**
 * Global authentication store instance
 * Export this to use throughout the application
 */
export const authStore = createAuthStore();

/**
 * Initialize auth store when module loads (browser only)
 */
if (browser) {
	authStore.initialize();
}
