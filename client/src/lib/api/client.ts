/**
 * API Client for communicating with the Django backend
 *
 * This module provides a centralized HTTP client for making requests to the backend API.
 * It handles authentication tokens, request/response formatting, error handling,
 * and provides type-safe methods for all authentication-related API calls.
 */

import type {
	LoginCredentials,
	LoginResponse,
	TokenRefreshResponse,
	ApiResponse,
	AuthError
} from '$types/auth';
import { API_CONFIG, getApiUrl, debugLog } from '$lib/config/env';

/**
 * Custom error class for API-related errors
 */
export class ApiError extends Error {
	constructor(
		message: string,
		public status: number,
		public data?: AuthError
	) {
		super(message);
		this.name = 'ApiError';
	}
}

/**
 * HTTP client class with authentication support
 *
 * Provides methods for making authenticated requests to the backend API.
 * Automatically handles JWT tokens, request formatting, and error responses.
 */
class ApiClient {
	private baseUrl: string;
	private accessToken: string | null = null;

	constructor(baseUrl: string = API_CONFIG.BASE_URL) {
		this.baseUrl = baseUrl;
		debugLog('ApiClient initialized with base URL:', baseUrl);
	}

	/**
	 * Set the access token for authenticated requests
	 */
	setAccessToken(token: string | null) {
		this.accessToken = token;
	}

	/**
	 * Get the current access token
	 */
	getAccessToken(): string | null {
		return this.accessToken;
	}

	/**
	 * Make a generic HTTP request with proper error handling
	 */
	private async request<T>(
		endpoint: string,
		options: RequestInit = {}
	): Promise<ApiResponse<T>> {
		const url = `${this.baseUrl}${endpoint}`;

		// Default headers
		const headers: HeadersInit = {
			'Content-Type': 'application/json',
			...options.headers,
		};

		// Add authorization header if token is available
		if (this.accessToken) {
			headers.Authorization = `Bearer ${this.accessToken}`;
		}

		try {
			const response = await fetch(url, {
				...options,
				headers,
			});

			// Parse response body
			let data: any;
			try {
				data = await response.json();
			} catch {
				data = null;
			}

			// Handle successful responses
			if (response.ok) {
				return {
					success: true,
					data,
				};
			}

			// Handle error responses
			const error: AuthError = data || {};
			throw new ApiError(
				error.detail || `HTTP ${response.status}: ${response.statusText}`,
				response.status,
				error
			);

		} catch (error) {
			// Handle network errors and other exceptions
			if (error instanceof ApiError) {
				return {
					success: false,
					error: error.data,
					message: error.message,
				};
			}

			return {
				success: false,
				message: error instanceof Error ? error.message : 'Network error occurred',
			};
		}
	}

	/**
	 * Authenticate user with email and password
	 *
	 * @param credentials - User login credentials
	 * @returns Promise with login response containing tokens and user data
	 */
	async login(credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> {
		debugLog('Attempting login for user:', credentials.email);

		const response = await this.request<LoginResponse>(API_CONFIG.ENDPOINTS.LOGIN, {
			method: 'POST',
			body: JSON.stringify(credentials),
		});

		// Store access token if login was successful
		if (response.success && response.data) {
			this.setAccessToken(response.data.access);
			debugLog('Login successful, token stored');
		} else {
			debugLog('Login failed:', response.error || response.message);
		}

		return response;
	}

	/**
	 * Refresh the access token using the refresh token
	 *
	 * @param refreshToken - JWT refresh token
	 * @returns Promise with new access token
	 */
	async refreshToken(refreshToken: string): Promise<ApiResponse<TokenRefreshResponse>> {
		debugLog('Attempting to refresh access token');

		const response = await this.request<TokenRefreshResponse>(API_CONFIG.ENDPOINTS.REFRESH, {
			method: 'POST',
			body: JSON.stringify({ refresh: refreshToken }),
		});

		// Update stored access token if refresh was successful
		if (response.success && response.data) {
			this.setAccessToken(response.data.access);
			debugLog('Token refresh successful');
		} else {
			debugLog('Token refresh failed:', response.error || response.message);
		}

		return response;
	}

	/**
	 * Get current user profile (requires authentication)
	 *
	 * @returns Promise with user profile data
	 */
	async getCurrentUser(): Promise<ApiResponse<any>> {
		debugLog('Fetching current user profile');
		return this.request(`${API_CONFIG.ENDPOINTS.USERS}me/`, {
			method: 'GET',
		});
	}

	/**
	 * Logout user (clear tokens)
	 * Note: This is a client-side operation since JWT tokens are stateless
	 */
	logout(): void {
		this.setAccessToken(null);
	}
}

/**
 * Singleton instance of the API client
 * Export this instance to use throughout the application
 */
export const apiClient = new ApiClient();

/**
 * Convenience functions for common API operations
 * These provide a simpler interface for components to use
 */
export const authApi = {
	/**
	 * Login with email and password
	 */
	login: (credentials: LoginCredentials) => apiClient.login(credentials),

	/**
	 * Refresh access token
	 */
	refreshToken: (refreshToken: string) => apiClient.refreshToken(refreshToken),

	/**
	 * Get current user profile
	 */
	getCurrentUser: () => apiClient.getCurrentUser(),

	/**
	 * Logout user
	 */
	logout: () => apiClient.logout(),

	/**
	 * Set access token for authenticated requests
	 */
	setAccessToken: (token: string | null) => apiClient.setAccessToken(token),

	/**
	 * Get current access token
	 */
	getAccessToken: () => apiClient.getAccessToken(),
};
